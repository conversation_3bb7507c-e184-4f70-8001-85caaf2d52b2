{"name": "travelsmart-front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"axios": "^1.7.9", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.468.0", "next": "15.4.1", "next-intl": "^3.22.4", "react": "19.1.0", "react-datepicker": "^7.5.0", "react-dom": "19.1.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}