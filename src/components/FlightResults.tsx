'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { Clock, Plane, Star, Shield, Timer, MapPin, Info, X, Award, Zap, CheckCircle, Search, Globe, Sparkles, TrendingUp, DollarSign, Zap as ZapIcon, Sunrise } from 'lucide-react';
import { FlightOffer } from '@/lib/api';
import { formatCurrency, formatTime, cn } from '@/lib/utils';
import FlexibleDates from './FlexibleDates';

interface Props {
  offers: FlightOffer[];
  onSelectFlight: (offer: FlightOffer) => void;
  passengerCount?: number;
  loading?: boolean;
  searchData?: {
    origin: string;
    destination: string;
    departureDate: string;
    returnDate?: string;
    passengers: Array<{ type: 'adult' | 'child' | 'infant' }>;
    cabinClass: 'economy' | 'premium_economy' | 'business' | 'first';
  };
  onDateSelect?: (departureDate: string, returnDate?: string) => void;
}

export default function FlightResults({ offers, onSelectFlight, passengerCount = 1, loading, searchData, onDateSelect }: Props) {
  const t = useTranslations();
  const [sortBy, setSortBy] = useState<'total_amount' | 'duration' | 'departure'>('total_amount');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [activeTab, setActiveTab] = useState<'recommended' | 'cheapest' | 'fastest' | 'earliest'>('recommended');
  const [tabChangeKey, setTabChangeKey] = useState(0);
  const [selectedOffer, setSelectedOffer] = useState<FlightOffer | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [visibleOffers, setVisibleOffers] = useState<FlightOffer[]>([]);
  const [loadingStep, setLoadingStep] = useState(0);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // Progressive loading animation
  useEffect(() => {
    let cancelled = false;
    if (!loading && offers.length > 0) {
      setVisibleOffers([]);
      setLoadingStep(0);
      setShowSuccessMessage(false);
      
      // Start progressive loading
      const loadOffers = async () => {
        for (let i = 0; i < offers.length; i++) {
          await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 200)); // Random delay between 300-500ms
          if (cancelled) return;
          setVisibleOffers(prev => [...prev, offers[i]]);
          setLoadingStep(i + 1);
        }
        
        // Show success message when all flights are loaded
        if (!cancelled) {
          setShowSuccessMessage(true);
          // Hide success message after 3 seconds
          setTimeout(() => {
            setShowSuccessMessage(false);
          }, 3000);
        }
      };
      
      loadOffers();
    }
    return () => {
      cancelled = true;
    };
  }, [loading, offers]);

  const handleSort = (newSortBy: typeof sortBy, tab: typeof activeTab) => {
    setActiveTab(tab);
    setTabChangeKey(prev => prev + 1); // Trigger re-render for animation
    
    // Set the correct sort parameters for each tab
    switch (tab) {
      case 'recommended':
        setSortBy('total_amount');
        setSortDirection('asc');
        break;
      case 'cheapest':
        setSortBy('total_amount');
        setSortDirection('asc');
        break;
      case 'fastest':
        setSortBy('duration');
        setSortDirection('asc');
        break;
      case 'earliest':
        setSortBy('departure');
        setSortDirection('asc');
        break;
    }
  };

  // Helper function to get the best price for each tab
  const getBestPrice = (tab: typeof activeTab) => {
    if (offers.length === 0) return 'US$394';
    
    let sortedOffers = [...offers];
    
    switch (tab) {
      case 'cheapest':
        sortedOffers.sort((a, b) => parseFloat(a.pricing.total_amount) - parseFloat(b.pricing.total_amount));
        break;
      case 'fastest':
        sortedOffers.sort((a, b) => {
          const durationA = a.flight_details.slices[0]?.duration || '';
          const durationB = b.flight_details.slices[0]?.duration || '';
          return durationA.localeCompare(durationB);
        });
        break;
      case 'earliest':
        sortedOffers.sort((a, b) => {
          const timeA = new Date(a.flight_details.slices[0]?.segments[0]?.departing_at || '').getTime();
          const timeB = new Date(b.flight_details.slices[0]?.segments[0]?.departing_at || '').getTime();
          return timeA - timeB;
        });
        break;
      default: // recommended
        // Keep original order (usually sorted by relevance)
        break;
    }
    
    return formatCurrency(parseFloat(sortedOffers[0].pricing.total_amount), sortedOffers[0].pricing.total_currency);
  };

  // Helper function to get flight count for each tab
  const getFlightCount = (tab: typeof activeTab) => {
    if (offers.length === 0) return 0;
    
    switch (tab) {
      case 'recommended':
        // Show count of direct flights for recommended
        return offers.filter(offer => offer.flight_details.slices[0]?.is_direct).length;
      case 'cheapest':
        // Show count of flights under average price
        const avgPrice = offers.reduce((sum, offer) => sum + parseFloat(offer.pricing.total_amount), 0) / offers.length;
        return offers.filter(offer => parseFloat(offer.pricing.total_amount) < avgPrice).length;
      case 'fastest':
        // Show count of direct flights (usually fastest)
        return offers.filter(offer => offer.flight_details.slices[0]?.is_direct).length;
      case 'earliest':
        // Show count of morning flights (before 12 PM)
        return offers.filter(offer => {
          const departureTime = new Date(offer.flight_details.slices[0]?.segments[0]?.departing_at || '').getHours();
          return departureTime < 12;
        }).length;
      default:
        return offers.length;
    }
  };

  const formatDuration = (duration: string) => {
    // Assuming duration is in format "PT2H30M"
    const match = duration.match(/PT(\d+H)?(\d+M)?/);
    if (!match) return duration;
    
    const hours = match[1] ? match[1].replace('H', 'h ') : '';
    const minutes = match[2] ? match[2].replace('M', 'm') : '';
    return `${hours}${minutes}`;
  };

  if (loading) {
    return (
      <div className="space-y-8">
        {/* Modern Loading Header */}
        <div className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-2xl p-8 border border-blue-100 shadow-sm">
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center animate-pulse">
                <Plane className="h-8 w-8 text-white animate-bounce" />
              </div>
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                <Search className="h-3 w-3 text-white" />
              </div>
            </div>
          </div>
          
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold text-gray-900 animate-pulse">
              {t('search.searchingTitle')}
            </h2>
            <p className="text-gray-600 max-w-md mx-auto">
              {t('search.searchingSubtitle')}
            </p>
            
            {/* Animated Progress Bar */}
            <div className="max-w-md mx-auto">
              <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full animate-gradient-x"></div>
              </div>
            </div>
            
            {/* Loading Steps */}
            <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>{t('search.checkingAirlines')}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                <span>{t('search.findingRoutes')}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                <span>{t('search.comparingPrices')}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Animated Flight Cards */}
        <div className="space-y-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <div 
              key={i} 
              className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 transform transition-all duration-700 ease-out opacity-0 animate-slideInUp"
              style={{ 
                animationDelay: `${i * 0.2}s`,
                animationFillMode: 'forwards'
              }}
            >
              <div className="flex items-center justify-between">
                {/* Airline Info */}
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white font-bold text-sm animate-pulse">
                      <Plane className="h-6 w-6" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                      <Sparkles className="h-2 w-2 text-white" />
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                    <div className="h-3 bg-gray-100 rounded w-16 animate-pulse"></div>
                  </div>
                </div>

                {/* Flight Route */}
                <div className="flex items-center gap-6 flex-1 justify-center">
                  <div className="text-center space-y-2">
                    <div className="h-6 bg-gray-200 rounded w-16 animate-pulse"></div>
                    <div className="h-4 bg-gray-100 rounded w-12 animate-pulse"></div>
                  </div>
                  
                  <div className="flex flex-col items-center space-y-2">
                    <div className="flex items-center w-24">
                      <div className="h-px bg-gray-300 flex-1"></div>
                      <Plane className="h-4 w-4 text-gray-400 mx-2 transform rotate-90 animate-bounce" />
                      <div className="h-px bg-gray-300 flex-1"></div>
                    </div>
                    <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                    <div className="h-3 bg-gray-100 rounded w-16 animate-pulse"></div>
                  </div>
                  
                  <div className="text-center space-y-2">
                    <div className="h-6 bg-gray-200 rounded w-16 animate-pulse"></div>
                    <div className="h-4 bg-gray-100 rounded w-12 animate-pulse"></div>
                  </div>
                </div>

                {/* Price */}
                <div className="text-right space-y-2">
                  <div className="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
                  <div className="h-4 bg-gray-100 rounded w-16 animate-pulse"></div>
                  <div className="h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg w-20 animate-pulse"></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Loading Message */}
        <div className="text-center py-8">
          <div className="inline-flex items-center gap-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full px-6 py-3 border border-blue-100">
            <Globe className="h-5 w-5 text-blue-600 animate-spin" />
            <span className="text-gray-700 font-medium">{t('search.searchingWorldwide')}</span>
          </div>
        </div>
      </div>
    );
  }

  if (offers.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-12 max-w-md mx-auto">
          <div className="text-6xl mb-6 flex justify-center">
            <Plane className="h-16 w-16 text-blue-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {t('search.noResults')}
          </h3>
          <p className="text-gray-600 mb-6">
            We couldn&apos;t find any flights matching your criteria. Try adjusting your search parameters.
          </p>
          <div className="space-y-2 text-sm text-gray-500">
            <div>• Try different dates</div>
            <div>• Check nearby airports</div>
            <div>• Consider flexible travel options</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header & Sort Controls */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-sm border border-blue-100 p-6 mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center gap-2">
              <Plane className="h-6 w-6 text-blue-600" />
              {offers.length} {t('search.resultsFound')}
              <span className="text-sm font-normal text-gray-600 ml-2">
                • {t(`results.${activeTab}`)}
              </span>
              {loadingStep > 0 && loadingStep < offers.length && (
                <span className="text-sm font-normal text-gray-600 ml-2">
                  {t('results.loadedCount', { loaded: loadingStep, total: offers.length })}
                </span>
              )}
            </h2>
            <p className="text-gray-600">
              Best deals sorted by price • Updated just now
            </p>
            
            {/* Natural Progress Bar */}
            {loadingStep < offers.length && (
              <div className="mt-4 max-w-md">
                <div className={`bg-gray-200 rounded-full h-1.5 overflow-hidden relative ${loadingStep === offers.length && offers.length > 0 ? 'progress-bar-complete' : ''}`}>
                  <div 
                    className={`h-full rounded-full transition-all duration-500 ${
                      loadingStep === offers.length && offers.length > 0 
                        ? 'bg-gradient-to-r from-green-500 to-emerald-500' 
                        : 'progress-bar-fill'
                    }`}
                    style={{ 
                      width: loadingStep > 0 ? `${Math.min((loadingStep / offers.length) * 100, 100)}%` : '0%'
                    }}
                  ></div>
                  {/* Shimmer overlay for active progress */}
                  {loadingStep > 0 && loadingStep < offers.length && (
                    <div 
                      className="progress-bar-shimmer absolute top-0 left-0 h-full rounded-full opacity-30"
                      style={{ 
                        width: `${Math.min((loadingStep / offers.length) * 100, 100)}%`
                      }}
                    ></div>
                  )}
                  {/* Initial loading indicator when no flights loaded yet */}
                  {loadingStep === 0 && offers.length > 0 && (
                    <div className="absolute top-0 left-0 h-full w-8 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-shimmer progress-bar-initial"></div>
                  )}
                </div>
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>{loadingStep === offers.length && offers.length > 0 ? t('search.completeStatus') : t('search.searching')}</span>
                  <span>{loadingStep > 0 ? `${Math.round((loadingStep / offers.length) * 100)}%` : '0%'} {t('search.complete')}</span>
                </div>
              </div>
            )}
            
            {/* Success message when all flights are loaded */}
            {showSuccessMessage && (
              <div className="mt-4 flex items-center gap-2 text-sm text-green-600 animate-fade-in">
                <CheckCircle className="h-4 w-4" />
                <span>{t('search.allFlightsLoaded')}</span>
              </div>
            )}
          </div>

          <div className="flex flex-wrap gap-3">
            {/* Modern Tab System */}
            <div className="bg-white rounded-xl p-1 shadow-sm border border-gray-100 flex tab-hover-effect">
              <button
                onClick={() => handleSort('total_amount', 'recommended')}
                className={cn(
                  "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 flex flex-col items-center gap-1 min-w-[95px]",
                  activeTab === 'recommended'
                    ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg tab-active-glow"
                    : "text-gray-600 hover:text-blue-600 hover:bg-blue-50"
                )}
              >
                <TrendingUp className="h-3 w-3" />
                <span className="text-xs opacity-80">{t('results.recommended')}</span>
                <span className="font-bold">
                  {getBestPrice('recommended')}
                </span>
                <span className="text-xs opacity-60">({getFlightCount('recommended')} {t('results.direct')})</span>
              </button>
              
              <button
                onClick={() => handleSort('total_amount', 'cheapest')}
                className={cn(
                  "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 flex flex-col items-center gap-1 min-w-[95px]",
                  activeTab === 'cheapest'
                    ? "bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg tab-active-glow green"
                    : "text-gray-600 hover:text-green-600 hover:bg-green-50"
                )}
              >
                <DollarSign className="h-3 w-3" />
                <span className="text-xs opacity-80">{t('results.cheapest')}</span>
                <span className="font-bold">
                  {getBestPrice('cheapest')}
                </span>
                <span className="text-xs opacity-60">({getFlightCount('cheapest')} {t('results.deals')})</span>
              </button>
              
              <button
                onClick={() => handleSort('duration', 'fastest')}
                className={cn(
                  "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 flex flex-col items-center gap-1 min-w-[95px]",
                  activeTab === 'fastest'
                    ? "bg-gradient-to-r from-purple-500 to-pink-600 text-white shadow-lg tab-active-glow purple"
                    : "text-gray-600 hover:text-purple-600 hover:bg-purple-50"
                )}
              >
                <ZapIcon className="h-3 w-3" />
                <span className="text-xs opacity-80">{t('results.fastest')}</span>
                <span className="font-bold">
                  {getBestPrice('fastest')}
                </span>
                <span className="text-xs opacity-60">({getFlightCount('fastest')} {t('results.direct')})</span>
              </button>
              
              <button
                onClick={() => handleSort('departure', 'earliest')}
                className={cn(
                  "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 flex flex-col items-center gap-1 min-w-[95px]",
                  activeTab === 'earliest'
                    ? "bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg tab-active-glow orange"
                    : "text-gray-600 hover:text-orange-600 hover:bg-orange-50"
                )}
              >
                <Sunrise className="h-3 w-3" />
                <span className="text-xs opacity-80">{t('results.earliest')}</span>
                <span className="font-bold">
                  {getBestPrice('earliest')}
                </span>
                <span className="text-xs opacity-60">({getFlightCount('earliest')} {t('results.morning')})</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Flexible Dates */}
      {searchData && (
        <div className="max-w-4xl mx-auto mb-6">
          <FlexibleDates
            origin={searchData.origin}
            destination={searchData.destination}
            departureDate={searchData.departureDate}
            returnDate={searchData.returnDate}
            passengers={searchData.passengers}
            cabinClass={searchData.cabinClass}
            onDateSelect={onDateSelect}
          />
        </div>
      )}

      {/* Flight Offers */}
      <div className="max-w-4xl mx-auto space-y-4">
        {(() => {
          // Sort offers based on active tab
          let sortedOffers = [...visibleOffers];
          
          switch (activeTab) {
            case 'cheapest':
              sortedOffers.sort((a, b) => parseFloat(a.pricing.total_amount) - parseFloat(b.pricing.total_amount));
              break;
            case 'fastest':
              sortedOffers.sort((a, b) => {
                const durationA = a.flight_details.slices[0]?.duration || '';
                const durationB = b.flight_details.slices[0]?.duration || '';
                return durationA.localeCompare(durationB);
              });
              break;
            case 'earliest':
              sortedOffers.sort((a, b) => {
                const timeA = new Date(a.flight_details.slices[0]?.segments[0]?.departing_at || '').getTime();
                const timeB = new Date(b.flight_details.slices[0]?.segments[0]?.departing_at || '').getTime();
                return timeA - timeB;
              });
              break;
            default: // recommended
              // Keep original order (usually sorted by relevance)
              break;
          }
          
          // Deduplicate offers by external_id or id
          const seen = new Set();
          const uniqueOffers = [];
          for (const offer of sortedOffers) {
            const key = offer.external_id || offer.id;
            if (!seen.has(key)) {
              seen.add(key);
              uniqueOffers.push(offer);
            }
          }
          return uniqueOffers.map((offer, index) => (
            <div
              key={`${offer.external_id || offer.id}-${index}-${tabChangeKey}`}
              className="flight-card group bg-white rounded-xl shadow-md border border-gray-100 hover:shadow-lg hover:border-blue-200 transition-all duration-300 overflow-hidden opacity-0 transform translate-y-4"
              style={{ 
                animationDelay: `${index * 0.1}s`,
                animationFillMode: 'forwards'
              }}
            >
              {/* Best Deal Badge */}
              {index === 0 && (
                <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-center py-1 px-4 flex items-center justify-center gap-1">
                  <Award className="h-3 w-3" />
                  <span className="text-xs font-semibold">Best Deal</span>
                </div>
              )}

              <div className="p-4">
                {offer.flight_details.slices.map((slice, sliceIndex: number) => (
                  <div key={sliceIndex} className={cn("", sliceIndex > 0 && "mt-3 pt-3 border-t border-gray-100")}>
                    {/* Compact Flight Info */}
                    <div className="grid grid-cols-12 gap-4 items-center">
                      {/* Airline & Flight Info - 3 cols */}
                      <div className="col-span-3 flex items-center gap-2">
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xs overflow-hidden flex-shrink-0">
                          {slice.segments[0]?.marketing_carrier.logo_symbol_url ? (
                            <Image
                              src={slice.segments[0].marketing_carrier.logo_symbol_url}
                              alt={offer.airline.name}
                              width={32}
                              height={32}
                              className="w-full h-full object-contain"
                            />
                          ) : null}
                          <div className={`w-full h-full flex items-center justify-center ${slice.segments[0]?.marketing_carrier.logo_symbol_url ? 'hidden' : 'flex'}`}>
                            {offer.airline.code}
                          </div>
                        </div>
                        <div className="min-w-0">
                          <div className="font-medium text-gray-900 text-sm truncate">{offer.airline.name}</div>
                          <div className="text-xs text-gray-500">
                            {slice.segments[0]?.marketing_carrier.iata_code}{slice.segments[0]?.marketing_carrier_flight_number}
                          </div>
                        </div>
                      </div>

                      {/* Departure - 2 cols */}
                      <div className="col-span-2 text-center">
                        <div className="text-lg font-bold text-gray-900">
                          {formatTime(new Date(slice.segments[0]?.departing_at))}
                        </div>
                        <div className="text-sm font-semibold text-blue-600">
                          {slice.origin.iata_code}
                        </div>
                      </div>

                      {/* Flight Path - 2 cols */}
                      <div className="col-span-2 flex flex-col items-center">
                        <div className="flex items-center w-full">
                          <div className="h-px bg-gray-300 flex-1"></div>
                          <Plane className="h-3 w-3 text-gray-400 mx-2 transform rotate-90" />
                          <div className="h-px bg-gray-300 flex-1"></div>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {formatDuration(slice.duration)}
                        </div>
                        <div className="text-xs">
                          {slice.is_direct ? (
                            <span className="text-green-600 flex items-center gap-1">
                              <CheckCircle className="h-3 w-3" />
                              Direct
                            </span>
                          ) : (
                            <span className="text-orange-600">{slice.total_stops} stop</span>
                          )}
                        </div>
                      </div>

                      {/* Arrival - 2 cols */}
                      <div className="col-span-2 text-center">
                        <div className="text-lg font-bold text-gray-900">
                          {formatTime(new Date(slice.segments[slice.segments.length - 1]?.arriving_at))}
                        </div>
                        <div className="text-sm font-semibold text-blue-600">
                          {slice.destination.iata_code}
                        </div>
                      </div>

                      {/* Price & Actions - 3 cols */}
                      <div className="col-span-3 text-right">
                        <div className="flex flex-col items-end mb-2">
                          <span className="text-xl font-bold text-blue-600">
                            {formatCurrency(parseFloat(offer.pricing.total_amount), offer.pricing.total_currency)}
                          </span>
                          <span className="text-xs text-gray-500">{t('results.totalFor', { count: passengerCount })}</span>
                          {passengerCount > 1 && (
                            <span className="text-sm text-gray-700 mt-1">
                              {formatCurrency(parseFloat(offer.pricing.total_amount) / passengerCount, offer.pricing.total_currency)}
                              <span className="ml-1 text-xs text-gray-500">{t('results.perPerson')}</span>
                            </span>
                          )}
                        </div>
                        <div className="flex gap-2 justify-end">
                          <button
                            onClick={() => {
                              setSelectedOffer(offer);
                              setShowModal(true);
                            }}
                            className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-xs font-medium transition-colors flex items-center gap-1"
                          >
                            <Info className="h-3 w-3" />
                            Details
                          </button>
                          <button
                            onClick={() => onSelectFlight(offer)}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-1"
                          >
                            <Zap className="h-3 w-3" />
                            Select
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Additional Info - Only for connecting flights */}
                    {slice.segments.length > 1 && (
                      <div className="mt-3 pt-3 border-t border-gray-100">
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>Stops: {slice.segments.map((s) => s.destination.iata_code).slice(0, -1).join(', ')}</span>
                          <span>•</span>
                          <span>Aircraft: {slice.segments[0]?.aircraft?.name || 'Various'}</span>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ));
        })()}
      </div>

      {/* Flight Details Modal */}
      {showModal && selectedOffer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                <Plane className="h-5 w-5 text-blue-600" />
                Flight Details
              </h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              {/* Airline Info */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 mb-6">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white font-bold text-lg overflow-hidden">
                    {selectedOffer.flight_details.slices[0]?.segments[0]?.marketing_carrier.logo_symbol_url ? (
                      <Image
                        src={selectedOffer.flight_details.slices[0].segments[0].marketing_carrier.logo_symbol_url}
                        alt={selectedOffer.airline.name}
                        width={64}
                        height={64}
                        className="w-full h-full object-contain"
                      />
                    ) : (
                      selectedOffer.airline.code
                    )}
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900">{selectedOffer.airline.name}</h4>
                    <p className="text-gray-600">
                      Flight {selectedOffer.flight_details.slices[0]?.segments[0]?.marketing_carrier.iata_code}
                      {selectedOffer.flight_details.slices[0]?.segments[0]?.marketing_carrier_flight_number}
                    </p>
                  </div>
                </div>
              </div>

              {/* Flight Segments */}
              {selectedOffer.flight_details.slices.map((slice, sliceIndex) => (
                <div key={sliceIndex} className="mb-6">
                  <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-blue-600" />
                    {slice.origin.city_name} → {slice.destination.city_name}
                  </h5>

                  {slice.segments.map((segment, segmentIndex) => (
                    <div key={segmentIndex} className="bg-white border border-gray-200 rounded-xl p-4 mb-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Departure */}
                        <div>
                          <h6 className="text-sm font-medium text-gray-500 mb-1">Departure</h6>
                          <div className="text-xl font-bold text-gray-900">
                            {formatTime(new Date(segment.departing_at))}
                          </div>
                          <div className="text-sm font-semibold text-blue-600">
                            {segment.origin.iata_code} - {segment.origin.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(segment.departing_at).toLocaleDateString()}
                          </div>
                        </div>

                        {/* Flight Info */}
                        <div className="text-center">
                          <h6 className="text-sm font-medium text-gray-500 mb-1">Flight</h6>
                          <div className="text-sm font-semibold text-gray-900">
                            {segment.marketing_carrier.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {segment.marketing_carrier.iata_code}{segment.marketing_carrier_flight_number}
                          </div>
                          <div className="text-xs text-gray-500">
                            {segment.aircraft?.name || 'Aircraft TBD'}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            Duration: {formatDuration(segment.duration)}
                          </div>
                        </div>

                        {/* Arrival */}
                        <div className="text-right">
                          <h6 className="text-sm font-medium text-gray-500 mb-1">Arrival</h6>
                          <div className="text-xl font-bold text-gray-900">
                            {formatTime(new Date(segment.arriving_at))}
                          </div>
                          <div className="text-sm font-semibold text-blue-600">
                            {segment.destination.iata_code} - {segment.destination.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(segment.arriving_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))}

              {/* Price Breakdown */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
                <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Star className="h-4 w-4 text-blue-600" />
                  Price Breakdown
                </h5>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Base fare:</span>
                    <span className="font-medium">{formatCurrency(parseFloat(selectedOffer.pricing.base_amount), selectedOffer.pricing.base_currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Taxes & fees:</span>
                    <span className="font-medium">{formatCurrency(parseFloat(selectedOffer.pricing.tax_amount), selectedOffer.pricing.tax_currency)}</span>
                  </div>
                  <div className="border-t border-blue-200 pt-2 mt-2">
                    <div className="flex justify-between">
                      <span className="text-lg font-bold text-gray-900">Total:</span>
                      <span className="text-lg font-bold text-blue-600">
                        {formatCurrency(parseFloat(selectedOffer.pricing.total_amount), selectedOffer.pricing.total_currency)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex items-center justify-between p-6 border-t border-gray-200">
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Shield className="h-4 w-4" />
                <span>Price locked for 10 minutes</span>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    setShowModal(false);
                    onSelectFlight(selectedOffer);
                  }}
                  className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2"
                >
                  <Zap className="h-4 w-4" />
                  Select Flight
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
