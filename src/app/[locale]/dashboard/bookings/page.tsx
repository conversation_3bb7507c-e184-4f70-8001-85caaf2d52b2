'use client';

import { useState, useEffect } from 'react';
import { useLocale } from 'next-intl';
import Link from 'next/link';
import { 
  Calendar, 
  MapPin, 
  Clock, 
  Plane, 
  Filter,
  Search,
  Download,
  Eye,
  X,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { bookingsApi } from '@/lib/api';

interface Booking {
  id: number;
  booking_reference: string;
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  departure_date: string;
  return_date?: string;
  origin: {
    city: string;
    airport_code: string;
  };
  destination: {
    city: string;
    airport_code: string;
  };
  passengers: number;
  total_amount: string;
  currency: string;
  airline: {
    name: string;
    code: string;
  };
  created_at: string;
}

export default function BookingsPage() {
  const locale = useLocale();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [filteredBookings, setFilteredBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    loadBookings();
  }, []);

  useEffect(() => {
    filterBookings();
  }, [bookings, searchTerm, statusFilter]);

  const loadBookings = async () => {
    try {
      // const response = await bookingsApi.getAllBookings();
      // setBookings(response.data.data);
      
      // Placeholder data for demonstration
      const mockBookings: Booking[] = [
        {
          id: 1,
          booking_reference: 'KT-2024-001',
          status: 'confirmed',
          departure_date: '2024-08-15T10:30:00Z',
          return_date: '2024-08-22T14:45:00Z',
          origin: { city: 'Tehran', airport_code: 'IKA' },
          destination: { city: 'Istanbul', airport_code: 'IST' },
          passengers: 2,
          total_amount: '450.00',
          currency: 'USD',
          airline: { name: 'Turkish Airlines', code: 'TK' },
          created_at: '2024-07-20T09:15:00Z'
        },
        {
          id: 2,
          booking_reference: 'KT-2024-002',
          status: 'pending',
          departure_date: '2024-09-10T16:20:00Z',
          origin: { city: 'Tehran', airport_code: 'IKA' },
          destination: { city: 'Dubai', airport_code: 'DXB' },
          passengers: 1,
          total_amount: '320.00',
          currency: 'USD',
          airline: { name: 'Emirates', code: 'EK' },
          created_at: '2024-07-25T14:30:00Z'
        },
        {
          id: 3,
          booking_reference: 'KT-2024-003',
          status: 'completed',
          departure_date: '2024-06-05T08:15:00Z',
          return_date: '2024-06-12T19:30:00Z',
          origin: { city: 'Tehran', airport_code: 'IKA' },
          destination: { city: 'London', airport_code: 'LHR' },
          passengers: 1,
          total_amount: '680.00',
          currency: 'USD',
          airline: { name: 'British Airways', code: 'BA' },
          created_at: '2024-05-15T11:45:00Z'
        }
      ];
      
      setBookings(mockBookings);
    } catch (error) {
      console.error('Error loading bookings:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterBookings = () => {
    let filtered = bookings;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(booking =>
        booking.booking_reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.destination.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.origin.city.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(booking => booking.status === statusFilter);
    }

    setFilteredBookings(filtered);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'pending':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-blue-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Bookings</h1>
          <p className="text-gray-600">Manage and track all your flight bookings</p>
        </div>
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Download className="h-4 w-4" />
            Export
          </button>
          <Link
            href={`/${locale}/flights`}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plane className="h-4 w-4" />
            Book New Flight
          </Link>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search by booking reference, destination..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Status Filter */}
          <div className="sm:w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              <option value="confirmed">Confirmed</option>
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            <Filter className="h-4 w-4" />
            Filters
          </button>
        </div>
      </div>

      {/* Bookings List */}
      <div className="space-y-4">
        {filteredBookings.length > 0 ? (
          filteredBookings.map((booking) => (
            <div key={booking.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                {/* Booking Info */}
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <span className="text-lg font-semibold text-gray-900">
                      {booking.booking_reference}
                    </span>
                    <span className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                      {getStatusIcon(booking.status)}
                      {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Route */}
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-gray-900">
                          {booking.origin.city} ({booking.origin.airport_code})
                        </span>
                      </div>
                      <Plane className="h-4 w-4 text-blue-500 transform rotate-90" />
                      <span className="font-medium text-gray-900">
                        {booking.destination.city} ({booking.destination.airport_code})
                      </span>
                    </div>

                    {/* Date */}
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-700">
                        {formatDate(booking.departure_date)}
                        {booking.return_date && (
                          <span> - {formatDate(booking.return_date)}</span>
                        )}
                      </span>
                    </div>

                    {/* Airline */}
                    <div className="flex items-center gap-2">
                      <Plane className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-700">
                        {booking.airline.name} ({booking.airline.code})
                      </span>
                    </div>

                    {/* Passengers */}
                    <div className="flex items-center gap-2">
                      <span className="text-gray-700">
                        {booking.passengers} passenger{booking.passengers > 1 ? 's' : ''}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Price and Actions */}
                <div className="flex flex-col sm:flex-row lg:flex-col items-start sm:items-center lg:items-end gap-3">
                  <div className="text-right">
                    <p className="text-2xl font-bold text-gray-900">
                      {booking.currency} {booking.total_amount}
                    </p>
                    <p className="text-sm text-gray-500">
                      Booked on {formatDate(booking.created_at)}
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <button className="flex items-center gap-2 px-3 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors">
                      <Eye className="h-4 w-4" />
                      View Details
                    </button>
                    {booking.status === 'confirmed' && (
                      <button className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                        <Download className="h-4 w-4" />
                        Download
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
            <Plane className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
            <p className="text-gray-500 mb-6">
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'You haven\'t made any bookings yet. Start planning your next trip!'
              }
            </p>
            <Link
              href={`/${locale}/flights`}
              className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plane className="h-4 w-4" />
              Search Flights
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
