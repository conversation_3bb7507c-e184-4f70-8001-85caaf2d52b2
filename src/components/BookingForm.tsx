'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { User, Mail, Phone, CreditCard, Plus, Trash2, Briefcase, Info, Shield, Users, Clock, Plane, Star, Zap, CheckCircle, Search, Globe, Sparkles, TrendingUp, DollarSign, ZapIcon, Sunrise, MapPin, Timer, X as CloseIcon, ThumbsUp, Eye, EyeOff } from 'lucide-react';
import Image from 'next/image';
import { FlightOffer, bookingsApi, passengersApi, validateEmail as validateEmailApi } from '@/lib/api';
import { formatCurrency, cn } from '@/lib/utils';
import React from 'react';

interface PassengerType {
  id?: number;
  given_name: string;
  family_name: string;
  nationality: string;
  passport_number: string;
  passport_expiry_date: string;
  birthdate: string;
  gender: string;
}

const NATIONALITIES = [
  { code: 'IR', label: 'Iran' },
  { code: 'US', label: 'United States' },
  { code: 'DE', label: 'Germany' },
  { code: 'FR', label: 'France' },
  { code: 'TR', label: 'Turkey' },
  // ... add more as needed
];
const GENDERS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
];

function PassengerModal({ open, onClose, onSave, initial, mode }: {
  open: boolean;
  onClose: () => void;
  onSave: (data: PassengerType) => void;
  initial: PassengerType | null;
  mode: 'add' | 'edit';
}) {
  const [form, setForm] = useState<PassengerType>(
    initial || {
      given_name: '',
      family_name: '',
      nationality: '',
      passport_number: '',
      passport_expiry_date: '',
      birthdate: '',
      gender: '',
    }
  );
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const t = (x: string) => x; // TODO: useTranslations if needed

  if (!open) return null;

  function validate() {
    const errs: { [key: string]: string } = {};
    if (!form.given_name) errs.given_name = 'Required';
    if (!form.family_name) errs.family_name = 'Required';
    if (!form.nationality) errs.nationality = 'Required';
    if (!form.passport_number) errs.passport_number = 'Required';
    if (!form.passport_expiry_date) errs.passport_expiry_date = 'Required';
    if (!form.birthdate) errs.birthdate = 'Required';
    if (!form.gender) errs.gender = 'Required';
    setErrors(errs);
    return Object.keys(errs).length === 0;
  }

  function handleChange(e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) {
    setForm({ ...form, [e.target.name]: e.target.value });
  }

  function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (validate()) {
      onSave(form);
      onClose();
    }
  }

  return (
    <div className="fixed inset-0 z-60 flex items-center justify-center bg-transparent backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl border border-blue-200 w-full max-w-md p-8 relative animate-fade-in">
        <button className="absolute top-4 right-4 text-gray-500 hover:text-blue-600 bg-white rounded-full shadow p-1.5 border border-gray-200" onClick={onClose} aria-label="Close modal"><CloseIcon className="h-6 w-6" /></button>
        <h2 className="text-2xl font-bold mb-6 text-gray-900 text-center">{mode === 'edit' ? 'Edit Passenger' : 'Add Passenger'}</h2>
        <form onSubmit={handleSubmit} className="space-y-5">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">First Name *</label>
            <input name="given_name" value={form.given_name} onChange={handleChange} className={cn("w-full px-4 py-2 border rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-200", errors.given_name && 'border-red-400')} />
            {errors.given_name && <p className="text-xs text-red-500 mt-1">{errors.given_name}</p>}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Last Name *</label>
            <input name="family_name" value={form.family_name} onChange={handleChange} className={cn("w-full px-4 py-2 border rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-200", errors.family_name && 'border-red-400')} />
            {errors.family_name && <p className="text-xs text-red-500 mt-1">{errors.family_name}</p>}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Nationality *</label>
            <select name="nationality" value={form.nationality} onChange={handleChange} className={cn("w-full px-4 py-2 border rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-200", errors.nationality && 'border-red-400')}>
              <option value="">Select...</option>
              {NATIONALITIES.map(n => <option key={n.code} value={n.code}>{n.label}</option>)}
            </select>
            {errors.nationality && <p className="text-xs text-red-500 mt-1">{errors.nationality}</p>}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Passport Number *</label>
            <input name="passport_number" value={form.passport_number} onChange={handleChange} className={cn("w-full px-4 py-2 border rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-200", errors.passport_number && 'border-red-400')} />
            {errors.passport_number && <p className="text-xs text-red-500 mt-1">{errors.passport_number}</p>}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Passport Expiry Date *</label>
            <input type="date" name="passport_expiry_date" value={form.passport_expiry_date} onChange={handleChange} className={cn("w-full px-4 py-2 border rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-200", errors.passport_expiry_date && 'border-red-400')} />
            {errors.passport_expiry_date && <p className="text-xs text-red-500 mt-1">{errors.passport_expiry_date}</p>}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Birthdate *</label>
            <input type="date" name="birthdate" value={form.birthdate} onChange={handleChange} className={cn("w-full px-4 py-2 border rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-200", errors.birthdate && 'border-red-400')} />
            {errors.birthdate && <p className="text-xs text-red-500 mt-1">{errors.birthdate}</p>}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Gender *</label>
            <select name="gender" value={form.gender} onChange={handleChange} className={cn("w-full px-4 py-2 border rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-200", errors.gender && 'border-red-400')}>
              <option value="">Select...</option>
              {GENDERS.map(g => <option key={g.value} value={g.value}>{g.label}</option>)}
            </select>
            {errors.gender && <p className="text-xs text-red-500 mt-1">{errors.gender}</p>}
          </div>
          <div className="flex justify-end gap-2 mt-6">
            <button type="button" onClick={onClose} className="px-4 py-2 rounded-md bg-gray-100 text-gray-700 font-medium hover:bg-gray-200">Cancel</button>
            <button type="submit" className="px-4 py-2 rounded-md bg-blue-600 text-white font-bold hover:bg-blue-700 shadow">{mode === 'edit' ? 'Save' : 'Add'}</button>
          </div>
        </form>
      </div>
    </div>
  );
}

function LoginModal({ open, onClose }: { open: boolean; onClose: () => void }) {
  const [form, setForm] = useState<{ identifier: string; password: string; otp: string }>({ identifier: '', password: '', otp: '' });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [mode, setMode] = useState<'password'|'otp'>('password');
  const [otpSent, setOtpSent] = useState(false);
  function validate() {
    const errs: { [key: string]: string } = {};
    if (!form.identifier) errs.identifier = 'Required';
    if (mode === 'password' && !form.password) errs.password = 'Required';
    if (mode === 'otp' && otpSent && !form.otp) errs.otp = 'Required';
    setErrors(errs);
    return Object.keys(errs).length === 0;
  }
  function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
    setForm({ ...form, [e.target.name]: e.target.value });
  }
  function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (!validate()) return;
    if (mode === 'password') {
      onClose();
      // TODO: real password login logic
    } else if (mode === 'otp') {
      if (!otpSent) {
        setOtpSent(true);
        alert('OTP sent (placeholder)');
      } else {
        onClose();
        // TODO: real OTP verify logic
      }
    }
  }
  function handleForgotPassword() {
    alert('Forgot Password (placeholder)');
  }
  function handleOtpLogin() {
    setMode('otp');
    setOtpSent(false);
    setForm(f => ({ ...f, otp: '', password: '' }));
    setErrors({});
  }
  function handleBackToPassword() {
    setMode('password');
    setOtpSent(false);
    setForm(f => ({ ...f, otp: '', password: '' }));
    setErrors({});
  }
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-60 flex items-center justify-center bg-transparent backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl border border-blue-200 w-full max-w-md p-8 relative animate-fade-in">
        <button className="absolute top-4 right-4 text-gray-500 hover:text-blue-600 bg-white rounded-full shadow p-1.5 border border-gray-200" onClick={onClose} aria-label="Close modal"><CloseIcon className="h-6 w-6" /></button>
        <h2 className="text-2xl font-bold mb-6 text-gray-900 text-center">Login</h2>
        {mode === 'password' && (
          <>
            <form onSubmit={handleSubmit} className="space-y-5">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email or Phone *</label>
                <input name="identifier" value={form.identifier} onChange={handleChange} className={cn("w-full px-4 py-2 border rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-200", errors.identifier && 'border-red-400')} />
                {errors.identifier && <p className="text-xs text-red-500 mt-1">{errors.identifier}</p>}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Password *</label>
                <input type="password" name="password" value={form.password} onChange={handleChange} className={cn("w-full px-4 py-2 border rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-200", errors.password && 'border-red-400')} />
                {errors.password && <p className="text-xs text-red-500 mt-1">{errors.password}</p>}
                <div className="flex justify-end mt-2">
                  <button type="button" onClick={handleForgotPassword} className="text-xs text-blue-600 hover:underline font-medium">Forgot Password?</button>
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-6">
                <button type="button" onClick={onClose} className="px-4 py-2 rounded-md bg-gray-100 text-gray-700 font-medium hover:bg-gray-200">Cancel</button>
                <button type="submit" className="px-4 py-2 rounded-md bg-blue-600 text-white font-bold hover:bg-blue-700 shadow">Login</button>
              </div>
            </form>
            <div className="flex items-center my-6">
              <div className="flex-1 h-px bg-gray-200" />
              <span className="mx-3 text-gray-400 text-sm">or</span>
              <div className="flex-1 h-px bg-gray-200" />
            </div>
            <button
              type="button"
              onClick={handleOtpLogin}
              className="w-full py-3 rounded-lg bg-white border border-blue-200 text-blue-700 font-bold hover:bg-blue-50 shadow-sm transition"
            >
              Login with OTP
            </button>
          </>
        )}
        {mode === 'otp' && (
          <>
            <form onSubmit={handleSubmit} className="space-y-5">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">OTP *</label>
                <input name="otp" value={form.otp} onChange={handleChange} className={cn("w-full px-4 py-2 border rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-200", errors.otp && 'border-red-400')} />
                {errors.otp && <p className="text-xs text-red-500 mt-1">{errors.otp}</p>}
              </div>
              <div className="flex justify-end gap-2 mt-6">
                <button type="button" onClick={handleBackToPassword} className="px-4 py-2 rounded-md bg-gray-100 text-gray-700 font-medium hover:bg-gray-200">Back to Password</button>
                <button type="submit" className="px-4 py-2 rounded-md bg-blue-600 text-white font-bold hover:bg-blue-700 shadow">Verify OTP</button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
}

interface BookingFormData {
  passengers: Array<{
    given_name: string;
    family_name: string;
    email: string;
    phone: string;
  }>;
  contact: {
    email: string;
    phone: string;
  };
}

interface Props {
  offer: FlightOffer;
  onBookingComplete: (bookingId: number) => void;
}

function getGuestToken() {
  let token = localStorage.getItem('guest_token');
  if (!token) {
    token = crypto.randomUUID();
    localStorage.setItem('guest_token', token);
  }
  return token;
}

export default function BookingForm({ offer, onBookingComplete }: Props) {
  const t = useTranslations();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [baggage, setBaggage] = useState(0);
  const [passengers, setPassengers] = useState<PassengerType[]>([]); // [{...fields}]
  const [passengersLoading, setPassengersLoading] = useState(false);
  const [passengersError, setPassengersError] = useState<string|null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'add'|'edit'>('add');
  const [editIndex, setEditIndex] = useState<number|null>(null);
  const [editInitial, setEditInitial] = useState<PassengerType | null>(null);
  const [contact, setContact] = useState({ email: '', phone: '' });
  const [contactErrors, setContactErrors] = useState({});
  const [loginModalOpen, setLoginModalOpen] = useState(false);
  const [contactName, setContactName] = useState('');
  const [contactEmail, setContactEmail] = useState('');
  const [contactEmailStatus, setContactEmailStatus] = useState<'idle'|'checking'|'registered'|'not-registered'|'error'>('idle');
  const [contactEmailError, setContactEmailError] = useState('');
  const [contactPassword, setContactPassword] = useState('');
  const [showContactPassword, setShowContactPassword] = useState(false);
  const [emailCheckTimeout, setEmailCheckTimeout] = useState<any>(null);

  // Assume isLoggedIn is available (replace with your auth logic)
  const isLoggedIn = false; // TODO: connect to your auth system
  const guest_token = !isLoggedIn ? getGuestToken() : undefined;

  function handleAuthClick() {
    // TODO: Replace with your auth modal/redirect logic
    alert('Redirect to Sign Up / Login');
  }

  // Load passengers from backend
  async function loadPassengers() {
    setPassengersLoading(true);
    setPassengersError(null);
    try {
      const res = await passengersApi.getAll(guest_token);
      setPassengers(res.data.data);
    } catch (e) {
      setPassengersError('Failed to load passengers');
    } finally {
      setPassengersLoading(false);
    }
  }

  // On mount, load passengers
  useEffect(() => {
    loadPassengers();
    // eslint-disable-next-line
  }, []);

  // Add passenger
  async function handleAddPassenger(data: PassengerType) {
    setPassengersLoading(true);
    setPassengersError(null);
    try {
      await passengersApi.add(data, guest_token);
      await loadPassengers();
    } catch (e) {
      setPassengersError('Failed to add passenger');
    } finally {
      setPassengersLoading(false);
    }
  }

  // Edit passenger
  async function handleEditPassenger(id: number, data: PassengerType) {
    setPassengersLoading(true);
    setPassengersError(null);
    try {
      await passengersApi.update(id, data, guest_token);
      await loadPassengers();
    } catch (e) {
      setPassengersError('Failed to update passenger');
    } finally {
      setPassengersLoading(false);
    }
  }

  // Delete passenger
  async function handleDeletePassenger(id: number) {
    setPassengersLoading(true);
    setPassengersError(null);
    try {
      await passengersApi.remove(id, guest_token);
      await loadPassengers();
    } catch (e) {
      setPassengersError('Failed to delete passenger');
    } finally {
      setPassengersLoading(false);
    }
  }

  function handleEmailChange(e: React.ChangeEvent<HTMLInputElement>) {
    setContactEmail(e.target.value);
    setContactEmailStatus('idle');
    setContactEmailError('');
    if (emailCheckTimeout) clearTimeout(emailCheckTimeout);
    setEmailCheckTimeout(setTimeout(() => validateEmail(e.target.value), 600));
  }

  async function validateEmail(email: string) {
    if (!email || !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email)) {
      setContactEmailStatus('idle');
      return;
    }
    setContactEmailStatus('checking');
    try {
      const res = await validateEmailApi(email);
      if (res.data.success) {
        setContactEmailStatus(res.data.registered ? 'registered' : 'not-registered');
      } else {
        setContactEmailStatus('error');
        setContactEmailError('Could not validate email');
      }
    } catch {
      setContactEmailStatus('error');
      setContactEmailError('Could not validate email');
    }
  }

  const onSubmit = async (data: BookingFormData) => {
    setLoading(true);
    setError(null);
    try {
      const bookingData = {
        offer_id: offer.id,
        passengers: data.passengers.map(p => ({
          given_name: p.given_name,
          family_name: p.family_name,
          email: p.email,
          phone: p.phone
        })),
        contact: data.contact,
        baggage,
        payments: [{
          type: 'balance',
          amount: offer.pricing.total_amount,
          currency: offer.pricing.total_currency
        }]
      };
      const response = await bookingsApi.book(bookingData);
      onBookingComplete(response.data.id);
    } catch (err) {
      setError(t('errors.bookingError'));
      console.error('Booking error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full px-2 md:px-8 py-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 w-full">
        {/* LEFT: Booking Form */}
        <div className="flex flex-col gap-8">
          {/* Who's Traveling? */}
          <div className="bg-white rounded-2xl shadow p-6 border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" /> Who's Traveling?
              </h3>
              {!isLoggedIn && (
                <button
                  type="button"
                  onClick={() => setLoginModalOpen(true)}
                  className="ml-2 px-3 py-1.5 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-lg text-sm font-medium border border-blue-100 transition shadow-sm"
                >
                  Login
                </button>
              )}
              <button
                type="button"
                onClick={() => { setModalMode('add'); setEditInitial(null); setModalOpen(true); }}
                className="flex items-center gap-1 px-3 py-1.5 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-lg text-sm font-medium border border-blue-100 transition"
              >
                <Plus className="h-4 w-4" /> Add Passenger
              </button>
            </div>
            <div className="space-y-4">
              {passengersLoading && <div className="text-center py-4">Loading passengers...</div>}
              {passengersError && <div className="text-center py-4 text-red-500">{passengersError}</div>}
              {passengers.length === 0 && !passengersLoading && !passengersError && <div className="text-gray-400 text-sm">No passengers added yet.</div>}
              {passengers.map((p, idx) => (
                <div key={p.id ?? idx} className="flex flex-col md:flex-row md:items-center gap-4 bg-gray-50 border border-blue-100 rounded-xl p-4 relative">
                  <div className="flex-1">
                    <div className="font-bold text-blue-900 text-lg">{p.given_name} {p.family_name}</div>
                    <div className="text-xs text-gray-500">Nationality: {NATIONALITIES.find(n => n.code === p.nationality)?.label || p.nationality}</div>
                    <div className="text-xs text-gray-500">Passport: {p.passport_number} | Exp: {p.passport_expiry_date}</div>
                    <div className="text-xs text-gray-500">Birthdate: {p.birthdate} | Gender: {GENDERS.find(g => g.value === p.gender)?.label || p.gender}</div>
                  </div>
                  <div className="flex gap-2 items-center">
                    <button onClick={() => { setModalMode('edit'); setEditIndex(idx); setEditInitial(p); setModalOpen(true); }} className="px-3 py-1 rounded-md bg-blue-100 text-blue-700 text-xs font-medium">Edit</button>
                    <button onClick={() => handleDeletePassenger(p.id!)} className="px-3 py-1 rounded-md bg-red-100 text-red-700 text-xs font-medium">Delete</button>
                </div>
              </div>
            ))}
            </div>
          </div>
          <PassengerModal
            open={modalOpen}
            onClose={() => setModalOpen(false)}
            onSave={data => {
              if (modalMode === 'add') handleAddPassenger(data);
              else if (modalMode === 'edit' && editInitial) handleEditPassenger(editInitial.id!, data);
            }}
            initial={editInitial}
            mode={modalMode}
          />
          <LoginModal open={loginModalOpen} onClose={() => setLoginModalOpen(false)} />

          {/* Contact Details */}
          <div className="bg-white rounded-2xl shadow p-6 border border-gray-100">
            <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <Mail className="h-5 w-5 text-blue-500" /> Contact Details
            </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Contact Name *</label>
                    <input
                      type="text"
                  name="contactName"
                  value={contactName}
                  onChange={e => setContactName(e.target.value)}
                  className="w-full px-4 py-2 border rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-200"
                    />
                  </div>
                <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                    <input
                  type="email"
                  name="contactEmail"
                  value={contactEmail}
                  onChange={handleEmailChange}
                  onBlur={() => validateEmail(contactEmail)}
                  className="w-full px-4 py-2 border rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-200"
                />
                {contactEmailStatus === 'checking' && <span className="text-xs text-blue-500 mt-1">Checking...</span>}
                {contactEmailStatus === 'error' && <span className="text-xs text-red-500 mt-1">{contactEmailError}</span>}
                {contactEmailStatus === 'registered' && <span className="text-xs text-green-600 mt-1">This email is already registered.</span>}
              </div>
                  </div>
            {contactEmailStatus === 'not-registered' && (
              <div className="mt-6 bg-blue-50 border border-blue-200 rounded-2xl p-6">
                <div className="mb-4 text-sm text-gray-700 font-medium">
                  Create an account with <span className="font-bold text-blue-700">{contactEmail}</span> to manage your bookings more conveniently
                </div>
                <div className="flex flex-row items-center gap-4">
                  {/* Passwordless Registration with badge above */}
                  <div className="flex-1 flex flex-col items-center relative">
                    <span className="absolute -top-3 left-0 flex items-center bg-green-100 text-green-700 px-2 py-0.5 rounded text-xs font-semibold shadow z-10">
                      <ThumbsUp className="h-4 w-4 mr-1" /> Recommended
                    </span>
                    <button
                      type="button"
                      className="w-full h-10 px-5 rounded-lg bg-blue-600 text-white font-bold shadow hover:scale-105 transition-all text-base"
                    >
                      Passwordless Registration
                    </button>
                  </div>
                  {/* Divider */}
                  <div className="flex flex-col items-center mx-3 relative h-10 justify-center">
                    <div className="w-px h-full bg-gray-200" />
                    <span className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white border border-gray-200 rounded-full px-2 py-0.5 text-gray-500 text-xs font-semibold shadow">or</span>
                  </div>
                  {/* Password Field */}
                  <div className="flex-1 flex flex-col items-center">
                    <div className="relative w-full">
                      <input
                        type={showContactPassword ? 'text' : 'password'}
                        className="w-full h-10 px-4 pr-10 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-300 text-gray-900 bg-white shadow placeholder-gray-400 text-base font-medium"
                        placeholder="Set your password to register"
                        value={contactPassword}
                        onChange={e => setContactPassword(e.target.value)}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-blue-600 focus:outline-none"
                        onClick={() => setShowContactPassword(v => !v)}
                        tabIndex={0}
                        aria-label={showContactPassword ? 'Hide password' : 'Show password'}
                      >
                        {showContactPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Additional Baggage Allowance */}
          <div className="bg-white rounded-2xl shadow p-6 border border-gray-100 flex flex-col gap-4">
            <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <Briefcase className="h-5 w-5 text-blue-500" /> Additional Baggage Allowance
            </h3>
            <div className="flex items-center gap-4">
              <label className="text-gray-700 font-medium">Extra Bags:</label>
              <button type="button" onClick={() => setBaggage(Math.max(0, baggage - 1))} className="px-3 py-1 bg-gray-100 rounded-l-lg text-lg font-bold">-</button>
              <span className="text-lg font-bold w-8 text-center">{baggage}</span>
              <button type="button" onClick={() => setBaggage(baggage + 1)} className="px-3 py-1 bg-gray-100 rounded-r-lg text-lg font-bold">+</button>
              <span className="text-gray-500 text-sm">(Each extra bag may incur additional fees)</span>
            </div>
          </div>

          {/* Cancellation & Change Policies */}
          <div className="bg-white rounded-2xl shadow p-6 border border-gray-100 flex flex-col gap-4">
            <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <Info className="h-5 w-5 text-blue-500" /> Cancellation & Change Policies
            </h3>
            <ul className="list-disc pl-6 text-gray-700 space-y-2">
              <li>Free cancellation within 24 hours of booking</li>
              <li>Change fees may apply depending on airline policy</li>
              <li>Non-refundable tickets are not eligible for refund</li>
              <li>Check your ticket for detailed fare rules</li>
            </ul>
            <div className="flex items-center gap-2 text-xs text-gray-500 mt-2">
              <Shield className="h-4 w-4" /> Secure booking & data protection
            </div>
          </div>

          {/* Error */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {/* Book Now Button */}
          <button
            type="submit"
            disabled={loading}
            className={cn(
              "w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-6 rounded-2xl font-bold text-lg shadow-lg transition-all",
              "hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
              loading && "opacity-50 cursor-not-allowed"
            )}
          >
            {loading ? t('common.loading') : `Book Now - ${formatCurrency(parseFloat(offer.pricing.total_amount), offer.pricing.total_currency)}`}
          </button>
        </div>
        {/* RIGHT: Flight Details Rich Summary */}
        <div className="flex flex-col gap-6">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-100 rounded-2xl shadow p-6 border border-blue-100">
            <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center gap-2">
              <Plane className="h-6 w-6 text-blue-600" /> Flight Details
            </h2>
            {offer.flight_details.slices.map((slice, sliceIdx) => (
              <div key={sliceIdx} className="mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <MapPin className="h-4 w-4 text-blue-500" />
                  <span className="font-semibold text-blue-900">{slice.origin.city_name} ({slice.origin.iata_code})</span>
                  <span className="mx-2 text-gray-400">→</span>
                  <span className="font-semibold text-blue-900">{slice.destination.city_name} ({slice.destination.iata_code})</span>
                  <span className="ml-4 text-xs text-gray-500">{slice.is_direct ? 'Direct' : `${slice.total_stops} stop(s)`}</span>
                </div>
                <div className="flex flex-col gap-2">
                  {slice.segments.map((segment, segIdx) => (
                    <div key={segIdx} className="flex flex-col md:flex-row md:items-center gap-4 bg-white rounded-xl border border-blue-100 p-4 mb-2 shadow-sm">
                      {/* Airline & Flight */}
                      <div className="flex items-center gap-3 min-w-[120px]">
                        {segment.marketing_carrier.logo_symbol_url ? (
                          <Image src={segment.marketing_carrier.logo_symbol_url} alt={segment.marketing_carrier.name} width={36} height={36} className="w-9 h-9 object-contain rounded-full bg-white border" />
                        ) : (
                          <div className="w-9 h-9 flex items-center justify-center bg-blue-100 rounded-full text-blue-700 font-bold text-lg">{segment.marketing_carrier.iata_code}</div>
                        )}
                        <div className="flex flex-col">
                          <span className="font-semibold text-gray-900 text-sm">{segment.marketing_carrier.name}</span>
                          <span className="text-xs text-gray-500">{segment.marketing_carrier.iata_code}{segment.marketing_carrier_flight_number}</span>
                        </div>
                      </div>
                      {/* Departure */}
                      <div className="flex-1 flex flex-col items-start">
                        <span className="text-xs text-gray-500">Departure</span>
                        <span className="font-bold text-blue-700 text-lg">{segment.origin.iata_code} - {segment.origin.name}</span>
                        <span className="text-xs text-gray-500">{new Date(segment.departing_at).toLocaleString()}</span>
                      </div>
                      {/* Timeline/Arrow */}
                      <div className="flex flex-col items-center">
                        <Plane className="h-5 w-5 text-blue-400 rotate-90" />
                        <span className="text-xs text-gray-500">{segment.aircraft?.name || 'Aircraft TBD'}</span>
                        <span className="text-xs text-gray-400">Duration: {segment.duration.replace('PT','').toLowerCase()}</span>
                      </div>
                      {/* Arrival */}
                      <div className="flex-1 flex flex-col items-end">
                        <span className="text-xs text-gray-500">Arrival</span>
                        <span className="font-bold text-blue-700 text-lg">{segment.destination.iata_code} - {segment.destination.name}</span>
                        <span className="text-xs text-gray-500">{new Date(segment.arriving_at).toLocaleString()}</span>
                      </div>
                    </div>
                  ))}
                  {/* Layover info */}
                  {slice.segments.length > 1 && slice.segments.map((segment, segIdx) => {
                    if (segIdx === slice.segments.length - 1) return null;
                    const layoverStart = new Date(segment.arriving_at);
                    const layoverEnd = new Date(slice.segments[segIdx+1].departing_at);
                    const diffMs = layoverEnd.getTime() - layoverStart.getTime();
                    const diffMin = Math.floor(diffMs/60000);
                    const hours = Math.floor(diffMin/60);
                    const mins = diffMin%60;
                    return (
                      <div key={"layover-"+segIdx} className="flex items-center gap-2 text-xs text-yellow-700 bg-yellow-50 rounded-lg px-3 py-1 my-1">
                        <Timer className="h-4 w-4" />
                        Layover in {segment.destination.city_name} ({segment.destination.iata_code}): {hours > 0 ? `${hours}h ` : ''}{mins}m
                      </div>
                    );
                  })}
                </div>
                {/* Slice duration */}
                <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                  <Clock className="h-4 w-4" /> Total duration: {slice.duration.replace('PT','').toLowerCase()}
                </div>
              </div>
            ))}
            {/* Provider & lock info */}
            <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
              <span>Provider: {offer.provider}</span>
              <Shield className="h-4 w-4" />
              <span>Booking expires: {new Date(offer.booking_info.expires_at).toLocaleString()}</span>
            </div>
            {/* Price breakdown */}
            <div className="bg-white rounded-xl border border-blue-100 p-4 shadow-sm w-full mt-4">
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-600">Base fare:</span>
                <span className="font-medium">{formatCurrency(parseFloat(offer.pricing.base_amount), offer.pricing.base_currency)}</span>
              </div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-600">Taxes & fees:</span>
                <span className="font-medium">{formatCurrency(parseFloat(offer.pricing.tax_amount), offer.pricing.tax_currency)}</span>
              </div>
              <div className="border-t border-blue-200 pt-2 mt-2 flex justify-between text-base font-bold">
                <span>Total:</span>
                <span className="text-blue-600">{formatCurrency(parseFloat(offer.pricing.total_amount), offer.pricing.total_currency)}</span>
              </div>
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-500 mt-1">
              <CheckCircle className="h-4 w-4 text-green-500" /> Price locked for 10 minutes
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
