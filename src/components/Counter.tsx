import React from 'react';

interface CounterProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  label?: string;
}

const Counter: React.FC<CounterProps> = ({ value, onChange, min = 0, max = 9, label }) => {
  return (
    <div className="flex flex-col items-center">
      {label && <span className="text-xs text-gray-500 mb-1">{label}</span>}
      <div className="flex items-center gap-2 bg-gray-50 rounded-full px-3 py-1">
        <button
          type="button"
          onClick={() => onChange(Math.max(min, value - 1))}
          className="w-7 h-7 flex items-center justify-center rounded-full text-xl text-gray-400 hover:text-blue-600 transition disabled:opacity-30 disabled:cursor-not-allowed"
          disabled={value <= min}
        >
          -
        </button>
        <span className="w-6 text-center font-semibold text-gray-700">{value}</span>
        <button
          type="button"
          onClick={() => onChange(Math.min(max, value + 1))}
          className="w-7 h-7 flex items-center justify-center rounded-full text-xl text-gray-400 hover:text-blue-600 transition disabled:opacity-30 disabled:cursor-not-allowed"
          disabled={value >= max}
        >
          +
        </button>
      </div>
    </div>
  );
};

export default Counter; 