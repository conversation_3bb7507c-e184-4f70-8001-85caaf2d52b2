'use client';

import { useState } from 'react';
import { flightsApi } from '@/lib/api';

export default function DebugSearchPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<unknown>(null);
  const [error, setError] = useState<string | null>(null);

  const testSearch = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    const searchData = {
      passengers: [{ type: 'adult' as const }],
      slices: [
        {
          origin: 'IST',
          destination: 'LHR',
          departure_date: '2025-12-25'
        }
      ],
      cabin_class: 'economy' as const
    };

    console.log('Sending search request:', searchData);

    try {
      const response = await flightsApi.search(searchData);
      console.log('Search response:', response);
      setResult(response.data);
    } catch (err: unknown) {
      console.error('Search error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Debug Flight Search</h1>
        
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Search Request</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm mb-4">
{`{
  "passengers": [{"type": "adult"}],
  "slices": [
    {
      "origin": "IST",
      "destination": "LHR",
      "departure_date": "2025-12-25"
    }
  ],
  "cabin_class": "economy"
}`}
          </pre>
          
          <button
            onClick={testSearch}
            disabled={loading}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Search'}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <h3 className="text-red-800 font-medium mb-2">Error:</h3>
            <pre className="text-red-700 text-sm whitespace-pre-wrap">
              {typeof error === 'object' ? JSON.stringify(error, null, 2) : error}
            </pre>
          </div>
        )}

        {result !== null && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-green-800 font-medium mb-2">Success Response:</h3>
            <pre className="text-green-700 text-sm whitespace-pre-wrap">
              {typeof result === 'object' ? JSON.stringify(result, null, 2) : String(result)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
