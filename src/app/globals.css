@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

/* Custom styles for react-datepicker - MODERN & BEAUTIFUL */
.react-datepicker {
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  border-radius: 1rem;
  box-shadow: 0 4px 24px 0 #0002;
  border: 1px solid #e0e7ef;
  padding: 0.5rem 0.5rem 0.7rem 0.5rem;
}
.react-datepicker__header {
  background: #f3f6fa;
  border-bottom: none;
  border-radius: 1rem 1rem 0 0;
  padding-top: 1rem;
}
.react-datepicker__current-month,
.react-datepicker-time__header,
.react-datepicker-year-header {
  font-weight: 700;
  color: #2563eb;
  font-size: 1.1rem;
}
.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
  color: #64748b;
  font-weight: 500;
  border-radius: 0.5rem;
  transition: background 0.2s, color 0.2s;
}
.react-datepicker__day:hover {
  background: #e0e7ef;
  color: #2563eb;
}
.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected {
  background: #2563eb;
  color: #fff !important;
  border-radius: 0.5rem;
}
.react-datepicker__day--today {
  border: 1.5px solid #2563eb;
  color: #2563eb;
  font-weight: 700;
}
.react-datepicker__day--outside-month {
  color: #cbd5e1;
}
.react-datepicker__navigation {
  top: 1.2rem;
}
.react-datepicker__navigation-icon::before {
  border-color: #2563eb;
}
.react-datepicker__triangle {
  display: none;
}
.react-datepicker__input-container input {
  font-weight: 600;
  letter-spacing: 0.01em;
}

/* Custom styles for react-datepicker */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container input {
  width: 100%;
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Fix input text visibility */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="number"],
select,
textarea {
  color: #1f2937 !important;
  background-color: #ffffff !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
  color: #1f2937 !important;
  background-color: #ffffff !important;
}

/* Placeholder text */
input::placeholder,
textarea::placeholder {
  color: #9ca3af !important;
  opacity: 1;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="number"],
  select,
  textarea {
    color: #1f2937 !important;
    background-color: #ffffff !important;
    border-color: #d1d5db !important;
  }
}

/* Flight card animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.flight-card {
  animation: slideInUp 0.6s ease-out forwards;
}

.flight-card:nth-child(1) { animation-delay: 0.1s; }
.flight-card:nth-child(2) { animation-delay: 0.2s; }
.flight-card:nth-child(3) { animation-delay: 0.3s; }
.flight-card:nth-child(4) { animation-delay: 0.4s; }
.flight-card:nth-child(5) { animation-delay: 0.5s; }

/* Enhanced slideInUp animation for loading cards */
.animate-slideInUp {
  animation: slideInUp 0.7s ease-out forwards;
}

/* Pulse animation for loading */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Animation for datepicker and segments */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(16px); }
  to { opacity: 1; transform: none; }
}
.animate-fadeInUp {
  animation: fadeInUp 0.3s cubic-bezier(.4,0,.2,1);
}

.react-datepicker {
  animation: fadeInUp 0.25s cubic-bezier(.4,0,.2,1);
}

/* Highlight Friday/Saturday (assuming en locale, adjust for fa if needed) */
.react-datepicker__day--friday,
.react-datepicker__day--saturday {
  background: #fef3c7;
  color: #b45309;
  border-radius: 0.5rem;
}

/* Hover effect with scale and shadow */
.react-datepicker__day:hover {
  background: #e0e7ef;
  color: #2563eb;
  transform: scale(1.08);
  box-shadow: 0 2px 8px #2563eb22;
}

/* Glow effect for search button */
.btn-glow:hover {
  box-shadow: 0 0 0 4px #2563eb33, 0 4px 24px #2563eb44;
  filter: brightness(1.08);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: none; }
}
.animate-fade-in {
  animation: fadeIn 0.5s;
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}
.animate-scale-in {
  animation: scaleIn 0.3s;
}

@keyframes gradientX {
  0% { background-position: 0% 50%; }
  100% { background-position: 100% 50%; }
}
.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradientX 2s linear infinite;
}

@keyframes bounceX {
  0% { transform: translateX(-50%) scale(1); }
  100% { transform: translateX(-50%) scale(1.15); }
}
.animate-bounce-x {
  animation: bounceX 1.2s infinite alternate;
}

/* Enhanced loading animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Progressive loading animation for flight cards */
@keyframes progressiveSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-progressive-slide-in {
  animation: progressiveSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Floating animation for loading elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Rotating gradient for progress bars */
@keyframes rotateGradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-rotate-gradient {
  background: linear-gradient(45deg, #3b82f6, #6366f1, #8b5cf6, #3b82f6);
  background-size: 400% 400%;
  animation: rotateGradient 3s ease infinite;
}

/* Progress bar shimmer effect */
@keyframes progressShimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.progress-bar-shimmer {
  background: linear-gradient(90deg, 
    #3b82f6 0%, 
    #6366f1 25%, 
    #8b5cf6 50%, 
    #6366f1 75%, 
    #3b82f6 100%
  );
  background-size: 200px 100%;
  animation: progressShimmer 2s linear infinite;
}

/* Smooth progress bar fill */
.progress-bar-fill {
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(90deg, #3b82f6, #6366f1, #8b5cf6);
  background-size: 200% 100%;
  animation: progressShimmer 3s linear infinite;
}

/* Initial progress bar pulse */
@keyframes progressPulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

.progress-bar-initial {
  animation: progressPulse 2s ease-in-out infinite;
}

/* Completion animation */
@keyframes completionGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

.progress-bar-complete {
  animation: completionGlow 1s ease-out;
}

/* Modern tab hover effects */
.tab-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Active tab glow effect */
.tab-active-glow {
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.tab-active-glow.green {
  box-shadow: 0 4px 20px rgba(34, 197, 94, 0.3);
}

.tab-active-glow.purple {
  box-shadow: 0 4px 20px rgba(147, 51, 234, 0.3);
}

.tab-active-glow.orange {
  box-shadow: 0 4px 20px rgba(249, 115, 22, 0.3);
}

/* Tab switching animation */
@keyframes tabSwitch {
  0% {
    opacity: 0.7;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.tab-switch-animation {
  animation: tabSwitch 0.3s ease-out;
}

/* Tab content fade transition */
.tab-content-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-content-transition:hover {
  transform: translateY(-1px);
}

/* Hide scrollbar for webkit browsers */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}
