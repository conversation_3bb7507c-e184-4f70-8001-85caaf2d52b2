'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Calendar, TrendingDown, TrendingUp, Clock, DollarSign } from 'lucide-react';
import { FlexibleDate, FlexibleSearchRequest, flightsApi } from '@/lib/api';
import { formatCurrency, formatDate } from '@/lib/utils';

interface Props {
  origin: string;
  destination: string;
  departureDate: string;
  returnDate?: string;
  passengers: Array<{ type: 'adult' | 'child' | 'infant' }>;
  cabinClass: 'economy' | 'premium_economy' | 'business' | 'first';
  onDateSelect?: (departureDate: string, returnDate?: string) => void;
}

export default function FlexibleDates({ 
  origin, 
  destination, 
  departureDate, 
  returnDate, 
  passengers, 
  cabinClass,
  onDateSelect 
}: Props) {
  const t = useTranslations();
  const [flexibleDates, setFlexibleDates] = useState<FlexibleDate[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);

  useEffect(() => {
    const fetchFlexibleDates = async () => {
      // Only fetch if we have valid departure date and required fields
      if (!departureDate || !origin || !destination) {
        return;
      }

      // Validate departure date
      const depDate = new Date(departureDate);
      
      if (isNaN(depDate.getTime())) {
        setError('Invalid date format');
        return;
      }

      // If return date exists, validate it
      if (returnDate) {
        const retDate = new Date(returnDate);
        
        if (isNaN(retDate.getTime())) {
          setError('Invalid return date format');
          return;
        }

        if (retDate <= depDate) {
          setError('Return date must be after departure date');
          return;
        }
      }

      // Ensure we have at least one passenger
      if (!passengers || passengers.length === 0) {
        setError('At least one passenger is required');
        return;
      }

      setLoading(true);
      setError(null);
      
      try {
        // Ensure dates are in YYYY-MM-DD format
        const formatDateForAPI = (dateStr: string) => {
          const date = new Date(dateStr);
          return date.toISOString().split('T')[0];
        };

        // If no return date, use one day after departure date
        const getDefaultReturnDate = (depDate: string) => {
          const date = new Date(depDate);
          date.setDate(date.getDate() + 1);
          return date.toISOString().split('T')[0];
        };

        const searchData: FlexibleSearchRequest = {
          origin,
          destination,
          departure_date: formatDateForAPI(departureDate),
          return_date: returnDate ? formatDateForAPI(returnDate) : getDefaultReturnDate(departureDate),
          passengers,
          cabin_class: cabinClass,
          flexible_days: 7
        };

        console.log('Flexible search request:', searchData);

        const response = await flightsApi.flexibleSearch(searchData);
        
        console.log('Flexible search response:', response);
        
        if (response.data.success) {
          setFlexibleDates(response.data.data.flexible_dates);
        } else {
          setError('Failed to load flexible dates');
        }
      } catch (err: any) {
        console.error('Error fetching flexible dates:', err);
        console.error('Error response:', err.response);
        console.error('Error request:', err.request);
        
        // Handle API error response
        if (err.response?.data?.errors) {
          const errors = err.response.data.errors;
          const errorMessages = Object.values(errors).flat();
          setError(errorMessages.join(', '));
        } else if (err.response?.data?.message) {
          setError(err.response.data.message);
        } else {
          setError('Failed to load flexible dates');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchFlexibleDates();
  }, [origin, destination, departureDate, returnDate, passengers, cabinClass]);

  const handleDateSelect = (dateInfo: FlexibleDate) => {
    if (dateInfo.available) {
      setSelectedDate(dateInfo.departure_date);
      onDateSelect?.(dateInfo.departure_date, dateInfo.return_date || undefined);
    }
  };

  const getLowestPrice = () => {
    const availableDates = flexibleDates.filter(date => date.available && date.price);
    if (availableDates.length === 0) return null;
    
    return availableDates.reduce((lowest, current) => {
      const currentPrice = parseFloat(current.price || '0');
      const lowestPrice = parseFloat(lowest.price || '0');
      return currentPrice < lowestPrice ? current : lowest;
    });
  };

  const getHighestPrice = () => {
    const availableDates = flexibleDates.filter(date => date.available && date.price);
    if (availableDates.length === 0) return null;
    
    return availableDates.reduce((highest, current) => {
      const currentPrice = parseFloat(current.price || '0');
      const highestPrice = parseFloat(highest.price || '0');
      return currentPrice > highestPrice ? current : highest;
    });
  };

  const lowestPrice = getLowestPrice();
  const highestPrice = getHighestPrice();

  if (loading) {
    return (
      <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
        <div className="flex items-center gap-3 mb-4">
          <Calendar className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">{t('results.flexibleDates')}</h3>
        </div>
        <div className="flex gap-2 overflow-x-auto pb-2">
          {[...Array(7)].map((_, i) => (
            <div key={i} className="flex-shrink-0 w-24 p-3 rounded-lg animate-pulse border border-gray-200">
              <div className="h-3 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-2 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
               <div className="text-center text-gray-500">
         <Calendar className="h-8 w-8 mx-auto mb-2 text-gray-400" />
         <p>{t('results.unableToLoad')}</p>
       </div>
      </div>
    );
  }

  if (flexibleDates.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            {/* Compact Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className="p-1.5 bg-blue-600 rounded-md">
            <Calendar className="h-4 w-4 text-white" />
          </div>
          <div>
            <h3 className="text-base font-semibold text-gray-900">{t('results.flexibleDates')}</h3>
            <p className="text-xs text-gray-500">Better prices around your dates</p>
          </div>
        </div>
        
        {/* Price Range */}
        {lowestPrice && highestPrice && (
          <div className="flex items-center gap-2 text-xs bg-gray-50 px-3 py-1.5 rounded-md">
            <div className="flex items-center gap-1 text-green-600">
              <TrendingDown className="h-3 w-3" />
              <span className="font-medium">{formatCurrency(parseFloat(lowestPrice.price || '0'), lowestPrice.currency)}</span>
            </div>
            <div className="w-px h-3 bg-gray-300"></div>
            <div className="flex items-center gap-1 text-red-600">
              <TrendingUp className="h-3 w-3" />
              <span className="font-medium">{formatCurrency(parseFloat(highestPrice.price || '0'), highestPrice.currency)}</span>
            </div>
          </div>
        )}
      </div>

            {/* Compact Dates Scroll */}
      <div className="relative">
        <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide">
          {flexibleDates.map((dateInfo, index) => {
            const animationDelay = index * 0.05;
            const isSelected = selectedDate === dateInfo.departure_date;
            const isAvailable = dateInfo.available && dateInfo.price;
            const isCurrentDate = dateInfo.departure_date === departureDate;
            
            return (
              <div
                key={`${dateInfo.departure_date}-${dateInfo.return_date || ''}-${index}`}
                onClick={() => handleDateSelect(dateInfo)}
                style={{ animationDelay: `${animationDelay}s` }}
                className={`
                  flex-shrink-0 w-24 p-3 rounded-lg cursor-pointer transition-all duration-200 animate-fade-in
                  ${isSelected 
                    ? 'bg-blue-600 text-white shadow-lg scale-110' 
                    : isCurrentDate
                    ? 'bg-blue-50 border border-blue-300 text-blue-900'
                    : 'bg-white border border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                  }
                  ${isAvailable ? 'hover:shadow-md hover:scale-105' : 'opacity-60'}
                `}
              >
                {/* Date */}
                <div className={`text-xs font-semibold mb-2 ${isSelected ? 'text-white' : 'text-gray-700'}`}>
                  {formatDate(dateInfo.departure_date, 'MMM dd')}
                </div>
                
                {/* Price or Status */}
                {isAvailable ? (
                  <div className={`text-sm font-bold ${isSelected ? 'text-white' : 'text-blue-600'}`}>
                    {formatCurrency(parseFloat(dateInfo.price || '0'), dateInfo.currency)}
                  </div>
                ) : (
                  <div className={`text-xs ${isSelected ? 'text-blue-100' : 'text-gray-500'}`}>
                    {t('results.view')}
                  </div>
                )}
                
                {/* Badges */}
                <div className="mt-2 space-y-1">
                  {isCurrentDate && !isSelected && (
                    <div className="text-xs text-blue-600 font-medium bg-blue-100 px-1.5 py-0.5 rounded-full">
                      {t('results.current')}
                    </div>
                  )}
                  
                  {isAvailable && lowestPrice && dateInfo.price === lowestPrice.price && (
                    <div className={`text-xs font-medium px-1.5 py-0.5 rounded-full ${isSelected ? 'bg-green-200 text-green-800' : 'bg-green-100 text-green-700'}`}>
                      {t('results.best')}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
        
        {/* Scroll Indicators */}
        <div className="absolute left-0 top-0 bottom-2 w-6 bg-gradient-to-r from-white to-transparent pointer-events-none"></div>
        <div className="absolute right-0 top-0 bottom-2 w-6 bg-gradient-to-l from-white to-transparent pointer-events-none"></div>
      </div>

            {/* Compact Footer */}
      <div className="mt-4 pt-3 border-t border-gray-100">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-blue-500" />
            <span>{t('results.daysAround')}</span>
          </div>
          <div className="flex items-center gap-1">
            <DollarSign className="h-3 w-3 text-green-500" />
            <span>{t('results.pricesMayVary')}</span>
          </div>
        </div>
        {!returnDate && (
          <div className="mt-2 text-xs text-blue-600 text-center bg-blue-50 py-1.5 rounded-md">
            ✈️ {t('results.oneWayPrices')}
          </div>
        )}
      </div>
    </div>
  );
} 