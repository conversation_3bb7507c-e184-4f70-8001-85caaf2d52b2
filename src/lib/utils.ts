import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number, currency: string = 'EUR'): string {
  return new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: currency,
  }).format(amount)
}

export function formatDate(date: Date | string, pattern?: string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (pattern) {
    // Custom pattern formatting
    const options: Intl.DateTimeFormatOptions = {};
    
    if (pattern.includes('MMM')) {
      options.month = 'short';
    } else if (pattern.includes('MM')) {
      options.month = '2-digit';
    } else if (pattern.includes('M')) {
      options.month = 'long';
    }
    
    if (pattern.includes('dd')) {
      options.day = '2-digit';
    } else if (pattern.includes('d')) {
      options.day = 'numeric';
    }
    
    if (pattern.includes('yyyy')) {
      options.year = 'numeric';
    } else if (pattern.includes('yy')) {
      options.year = '2-digit';
    }
    
    return new Intl.DateTimeFormat('en', options).format(dateObj);
  }
  
  // Default formatting
  return new Intl.DateTimeFormat('en', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(dateObj);
}

export function formatTime(date: Date): string {
  return new Intl.DateTimeFormat('en', {
    hour: '2-digit',
    minute: '2-digit',
  }).format(date)
}
