'use client';

import { useState, useRef, useEffect } from 'react';
import { useLocale } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { Plane, User, ChevronDown, LogOut, Settings, UserPlus, LogIn, LayoutDashboard } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';

const MENU = [
  { label: 'Flights', href: '' },
  { label: 'Hotels', href: '/hotels' },
  { label: 'Trains', href: '/trains' },
];

const LANGUAGES = [
  { code: 'en', label: 'English' },
  { code: 'de', label: 'Deutsch' },
  { code: 'fr', label: 'Français' },
  { code: 'es', label: 'Español' },
];

export default function Header() {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [langOpen, setLangOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const langRef = useRef(null);
  const userMenuRef = useRef(null);
  const { user, isAuthenticated, logout } = useAuth();

  const switchLocale = (newLocale: string) => {
    const segments = pathname.split('/');
    segments[1] = newLocale;
    router.push(segments.join('/'));
  };

  // Close dropdowns on outside click
  useEffect(() => {
    function handleClick(e: MouseEvent) {
      if (langRef.current && !(langRef.current as any).contains(e.target)) {
        setLangOpen(false);
      }
      if (userMenuRef.current && !(userMenuRef.current as any).contains(e.target)) {
        setUserMenuOpen(false);
      }
    }
    if (langOpen || userMenuOpen) {
      document.addEventListener('mousedown', handleClick);
    } else {
      document.removeEventListener('mousedown', handleClick);
    }
    return () => document.removeEventListener('mousedown', handleClick);
  }, [langOpen, userMenuOpen]);

  const handleLogout = async () => {
    await logout();
    setUserMenuOpen(false);
    router.push(`/${locale}`);
  };

  return (
    <header className="bg-white border-b border-gray-100 sticky top-0 z-40 w-full">
      <div className="max-w-7xl mx-auto px-4 sm:px-8 lg:px-12">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center gap-2">
            <Plane className="h-7 w-7 text-blue-600" />
            <span className="ml-1 text-lg font-bold text-gray-800 tracking-tight">Kiyans Trip</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-6">
            {MENU.map((item) => (
              <Link
                key={item.label}
                href={`/${locale}${item.href}`}
                className={`text-gray-700 hover:text-blue-600 font-medium transition-colors px-2 py-1 rounded ${pathname === `/${locale}${item.href}` ? 'text-blue-700 font-semibold' : ''}`}
              >
                {item.label}
              </Link>
            ))}
            {/* Language Dropdown */}
            <div className="relative" ref={langRef}>
              <button
                className="flex items-center gap-1 text-xs text-gray-500 hover:text-blue-600 font-semibold px-2 py-1 rounded border border-gray-200"
                onClick={() => setLangOpen((v) => !v)}
                aria-haspopup="listbox"
                aria-expanded={langOpen}
              >
                {LANGUAGES.find(l => l.code === locale)?.label || locale.toUpperCase()}
                <ChevronDown className="h-4 w-4" />
              </button>
              {langOpen && (
                <ul className="absolute right-0 mt-2 w-32 bg-white border border-gray-200 rounded shadow-lg z-50" role="listbox">
                  {LANGUAGES.map(lang => (
                    <li key={lang.code}>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm hover:bg-blue-50 ${locale === lang.code ? 'text-blue-700 font-semibold' : 'text-gray-700'}`}
                        onClick={() => {
                          setLangOpen(false);
                          if (lang.code !== locale) switchLocale(lang.code);
                        }}
                        role="option"
                        aria-selected={locale === lang.code}
                      >
                        {lang.label}
                      </button>
                    </li>
                  ))}
                </ul>
              )}
            </div>
            {/* User Menu */}
            {isAuthenticated ? (
              <div className="relative" ref={userMenuRef}>
                <button
                  className="flex items-center gap-2 bg-gray-100 hover:bg-blue-50 text-blue-700 px-4 py-2 rounded transition-all ml-2"
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                >
                  <User className="h-5 w-5" />
                  <span className="hidden sm:inline text-sm font-medium">{user?.name}</span>
                  <ChevronDown className="h-4 w-4" />
                </button>
                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    <div className="px-4 py-3 border-b border-gray-100">
                      <p className="text-sm font-medium text-gray-900">{user?.name}</p>
                      <p className="text-xs text-gray-500">{user?.email}</p>
                    </div>
                    <div className="py-1">
                      <Link
                        href={`/${locale}/dashboard`}
                        className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        <LayoutDashboard className="h-4 w-4" />
                        Dashboard
                      </Link>
                      <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        <Settings className="h-4 w-4" />
                        Settings
                      </button>
                      <button
                        onClick={handleLogout}
                        className="flex items-center gap-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                      >
                        <LogOut className="h-4 w-4" />
                        Logout
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center gap-2 ml-2">
                <Link
                  href={`/${locale}/login`}
                  className="flex items-center gap-2 text-blue-700 hover:text-blue-800 px-3 py-2 rounded transition-colors"
                >
                  <LogIn className="h-4 w-4" />
                  <span className="text-sm font-medium">Login</span>
                </Link>
                <Link
                  href={`/${locale}/register`}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
                >
                  <UserPlus className="h-4 w-4" />
                  <span className="text-sm font-medium">Register</span>
                </Link>
              </div>
            )}
          </nav>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden ml-2 p-2 rounded hover:bg-gray-100 transition-colors"
            aria-label="Open menu"
          >
            <svg className="h-6 w-6 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>
      </div>
      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t shadow-lg px-4 py-4">
          <nav className="flex flex-col gap-3">
            {MENU.map((item) => (
              <Link
                key={item.label}
                href={`/${locale}${item.href}`}
                className={`text-gray-700 hover:text-blue-600 font-medium transition-colors px-2 py-2 rounded ${pathname === `/${locale}${item.href}` ? 'text-blue-700 font-semibold' : ''}`}
                onClick={() => setIsMenuOpen(false)}
              >
                {item.label}
              </Link>
            ))}
            {/* Language Dropdown Mobile */}
            <div className="relative" ref={langRef}>
              <button
                className="flex items-center gap-1 text-xs text-gray-500 hover:text-blue-600 font-semibold px-2 py-1 rounded border border-gray-200"
                onClick={() => setLangOpen((v) => !v)}
                aria-haspopup="listbox"
                aria-expanded={langOpen}
              >
                {LANGUAGES.find(l => l.code === locale)?.label || locale.toUpperCase()}
                <ChevronDown className="h-4 w-4" />
              </button>
              {langOpen && (
                <ul className="absolute right-0 mt-2 w-32 bg-white border border-gray-200 rounded shadow-lg z-50" role="listbox">
                  {LANGUAGES.map(lang => (
                    <li key={lang.code}>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm hover:bg-blue-50 ${locale === lang.code ? 'text-blue-700 font-semibold' : 'text-gray-700'}`}
                        onClick={() => {
                          setLangOpen(false);
                          if (lang.code !== locale) switchLocale(lang.code);
                        }}
                        role="option"
                        aria-selected={locale === lang.code}
                      >
                        {lang.label}
                      </button>
                    </li>
                  ))}
                </ul>
              )}
            </div>
            {/* Mobile User Menu */}
            {isAuthenticated ? (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="px-2 py-2 mb-3">
                  <p className="text-sm font-medium text-gray-900">{user?.name}</p>
                  <p className="text-xs text-gray-500">{user?.email}</p>
                </div>
                <div className="space-y-1">
                  <Link
                    href={`/${locale}/dashboard`}
                    className="flex items-center gap-2 w-full px-2 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <LayoutDashboard className="h-4 w-4" />
                    Dashboard
                  </Link>
                  <button className="flex items-center gap-2 w-full px-2 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded">
                    <Settings className="h-4 w-4" />
                    Settings
                  </button>
                  <button
                    onClick={handleLogout}
                    className="flex items-center gap-2 w-full px-2 py-2 text-sm text-red-600 hover:bg-red-50 rounded"
                  >
                    <LogOut className="h-4 w-4" />
                    Logout
                  </button>
                </div>
              </div>
            ) : (
              <div className="mt-4 pt-4 border-t border-gray-200 space-y-2">
                <Link
                  href={`/${locale}/login`}
                  className="flex items-center gap-2 text-blue-700 hover:text-blue-800 px-2 py-2 rounded transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <LogIn className="h-4 w-4" />
                  <span className="text-sm font-medium">Login</span>
                </Link>
                <Link
                  href={`/${locale}/register`}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <UserPlus className="h-4 w-4" />
                  <span className="text-sm font-medium">Register</span>
                </Link>
              </div>
            )}
          </nav>
        </div>
      )}
    </header>
  );
}
